# 🤖 AI Sales Agent - MongoDB Single Source of Truth

> **Transform your sales outreach with AI agents powered by MongoDB's intelligent data platform**

An open-source AI sales automation system that demonstrates the **power of MongoDB as the central intelligence hub** for AI agents. This production-ready system shows how to build hyper-personalized sales outreach using MongoDB, Agno framework, and modern AI tools.

## ✨ **Why This Matters**

Traditional sales tools treat data as scattered pieces. This system shows how **MongoDB as a single source of truth** enables AI agents to:

- 🧠 **Remember Everything**: Complete conversation history, meeting notes, and interaction context
- 🔍 **Research Intelligently**: Combine CRM data with real-time company intelligence
- 💬 **Personalize Perfectly**: Generate messages that reference specific details and conversations
- 🚀 **Scale Effortlessly**: Handle hundreds of leads with consistent quality

## 🎯 **What You'll Build**

### **MongoDB-Powered Intelligence Hub**
- **Single Source of Truth**: All contact data, research, and interactions in MongoDB
- **AI Agent Coordination**: Agents share data seamlessly through MongoDB collections
- **Vector Search Ready**: Built for MongoDB Atlas Vector Search and AI applications
- **Real-Time Sync**: Live updates between CRM, research, and messaging systems

### **Hyper-Personalized Outreach Engine**
- **Deep CRM Analysis**: References specific notes, meetings, and interaction history
- **Research-Driven Messages**: Combines CRM data with real-time company intelligence
- **Quality Metrics**: Achieves >0.8 personalization scores and >0.4 response predictions
- **Business Context**: Configurable for any product/service (MongoDB, SaaS, consulting, etc.)

### **Production-Ready Architecture**
- **Chrome Extension**: One-click lead processing from any CRM
- **FastAPI Backend**: Scalable API with comprehensive error handling
- **WhatsApp Integration**: Reliable messaging via WhatsApp Web.js
- **Performance**: <60 seconds per lead, end-to-end processing

## 🔄 **How It Works**

```mermaid
graph TD
    A[CRM Click] --> B[Chrome Extension]
    B --> C[Extract Lead Data]
    C --> D[Store in MongoDB]
    D --> E[Research Agent]
    E --> F[Store Research in MongoDB]
    F --> G[Message Agent]
    G --> H[Fetch ALL Data from MongoDB]
    H --> I[Generate Hyper-Personalized Message]
    I --> J[WhatsApp Outreach]
    J --> K[Update MongoDB with Results]
```

### **MongoDB Single Source of Truth**
1. **📊 Contact Data**: Complete CRM data (notes, meetings, history) stored in MongoDB
2. **🔍 Research Intelligence**: Company research and insights indexed in MongoDB
3. **💬 Message Context**: All previous conversations and generated messages in MongoDB
4. **📈 Analytics**: Performance metrics, response rates, and optimization data in MongoDB

**Result**: Every AI agent has complete context, enabling truly intelligent conversations.

## 🚀 **Quick Start (5 Minutes)**

### **Prerequisites**
- Python 3.11+ and Node.js 16+
- [MongoDB Atlas](https://www.mongodb.com/atlas) (free tier works)
- API keys: [Monday.com](https://monday.com), [Tavily](https://tavily.com), [Google AI](https://ai.google.dev)

### **1. Clone & Install**
```bash
git clone <your-repo>
cd agno-sales-extension

# Install Agno framework
pip install agno

# Install backend dependencies
cd backend && pip install -r requirements.txt

# Install WhatsApp dependencies
cd ../whatsapp && npm install
```

### **2. Configure Environment**
```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your credentials:
MONGODB_CONNECTION_STRING=mongodb+srv://username:<EMAIL>/
MONDAY_API_TOKEN=your_monday_api_token
TAVILY_API_KEY=your_tavily_api_key
GEMINI_API_KEY=your_google_ai_api_key

# Configure your business context (optional)
BUSINESS_OWNER_NAME="Your Name"
COMPANY_NAME="Your Company"
PRODUCT_NAME="Your Product"
```

### **3. Verify Setup**
```bash
python verify_mongodb_single_source_truth.py
# Should show: ✅ All systems ready!
```

### **4. Start the System**
```bash
# Terminal 1: Start backend
cd backend && python main.py
# ✅ Backend running on http://localhost:8000

# Terminal 2: Start WhatsApp bridge
cd whatsapp && node working_bridge.js
# ✅ Scan QR code with your phone when prompted

# Terminal 3: Install Chrome extension
# 1. Open Chrome → Extensions → Developer mode
# 2. Click "Load unpacked" → Select extension/ folder
# 3. Navigate to your CRM and start processing leads!
```

### **5. Test Your First Lead**
1. **Open your CRM** (Monday.com, HubSpot, Salesforce, etc.)
2. **Click the AI agent button** that appears on lead rows
3. **Watch the magic**: Lead data → MongoDB → Research → Personalized message → WhatsApp
4. **See results**: >0.8 personalization score, intelligent message referencing specific CRM data

## 🎯 **Customize for Your Business**

### **🔄 Swap Out Any Component**

**CRM Integration** (Currently Monday.com):
```python
# backend/tools/your_crm_client.py
class YourCRMClient:
    def get_lead_data(self, lead_id):
        # Connect to Salesforce, HubSpot, Pipedrive, etc.
        return comprehensive_lead_data
```

**Research Tools** (Currently Tavily):
```python
# backend/agents/research_agent.py
def research_company(self, company_name):
    # Use Apollo, ZoomInfo, LinkedIn Sales Navigator, etc.
    return research_insights
```

**Messaging Channels** (Currently WhatsApp):
```python
# backend/agents/outreach_agent.py
def send_message(self, contact, message):
    # Send via Email, LinkedIn, Slack, SMS, etc.
    return delivery_status
```

**AI Models** (Currently Gemini):
```python
# Use OpenAI, Claude, Llama, or any LLM
from agno.models.openai import OpenAI
model = OpenAI(model="gpt-4")
```

### **🏢 Business Configuration**
```bash
# .env - Customize for your business
PRODUCT_NAME="Your Product"
COMPANY_NAME="Your Company"
VALUE_PROPOSITION="Your unique value prop"
SPECIALIZATIONS="AI,MongoDB,SaaS"  # Your expertise areas
RESEARCH_FOCUS="AI applications,data infrastructure"  # What to research
```

## 📊 **MongoDB as Single Source of Truth**

### **Why MongoDB Powers This System**

**🧠 Intelligent Data Storage**:
```javascript
// Complete lead context in one document
{
  _id: ObjectId("..."),
  crm_data: {
    name: "Sarah Chen",
    company: "TechCorp",
    notes: ["Interested in AI solutions", "Budget: $50k"],
    meetings: [{"date": "2024-01-15", "topic": "AI strategy"}]
  },
  research_data: {
    company_insights: "TechCorp raised $10M Series A...",
    decision_makers: ["Sarah Chen - CTO", "Mike Johnson - CEO"],
    tech_stack: ["Python", "AWS", "PostgreSQL"],
    confidence_score: 0.92
  },
  message_history: [
    {"sent": "2024-01-10", "response": "positive", "topic": "AI consultation"}
  ],
  ai_insights: {
    personalization_score: 0.87,
    best_approach: "technical_deep_dive",
    next_action: "schedule_demo"
  }
}
```

**🚀 What This Enables**:
- **Perfect Memory**: AI remembers every interaction, note, and context
- **Intelligent Research**: Combines CRM data with real-time company intelligence
- **Hyper-Personalization**: Messages reference specific conversations and needs
- **Continuous Learning**: System gets smarter with each interaction

**📈 Performance Benefits**:
- **Sub-second queries** with MongoDB's optimized indexing
- **Horizontal scaling** for thousands of leads
- **Vector search ready** for semantic similarity and AI applications
- **Real-time updates** across all AI agents

## 🏗️ **System Architecture**

### **🤖 AI Agents** (`backend/agents/`)
```python
# Research Agent - Gathers company intelligence
research_agent.research_company("TechCorp")
# → Stores insights in MongoDB for all agents to use

# Message Agent - Generates hyper-personalized messages
message_agent.generate_message(lead_data, research_data)
# → References specific CRM notes and research insights

# Outreach Agent - Handles multi-channel messaging
outreach_agent.send_message(contact, message, channel="whatsapp")
# → Tracks delivery and responses in MongoDB
```

### **🌐 Chrome Extension** (`extension/`)
- **Universal CRM Integration**: Works with Monday.com, Salesforce, HubSpot, etc.
- **One-Click Processing**: Extract lead data and trigger AI workflow
- **Real-Time Updates**: Live status and progress tracking

### **💬 Messaging Bridge** (`whatsapp/`)
- **WhatsApp Web.js**: Reliable messaging without API limits
- **Multi-Channel Ready**: Easy to extend for Email, LinkedIn, SMS
- **Delivery Tracking**: Monitor message status and responses

### **🗄️ MongoDB Collections**
```javascript
contacts      // Complete CRM data and interaction history
research      // Company intelligence and insights
messages      // Generated messages and performance metrics
workflows     // Processing status and analytics
```

## 🚀 **Real-World Results**

### **Message Quality Examples**

**Before (Generic)**:
> "Hi John, I'd like to discuss our database solutions for your company."

**After (Hyper-Personalized)**:
> "Hi John, saw your recent LinkedIn post about scaling TechCorp's AI infrastructure. Given your migration from PostgreSQL mentioned in our last call, MongoDB's horizontal scaling could solve those performance bottlenecks you described. Worth a 15-min chat about vector search for your recommendation engine?"

### **Performance Metrics**
- **⚡ Processing Speed**: <60 seconds per lead (research + message generation)
- **🎯 Personalization Score**: >0.8 (references specific CRM data and research)
- **📈 Response Rate**: 2-3x improvement over generic outreach
- **🔄 Scalability**: Handles 100+ leads simultaneously

## 🛠️ **Development & Testing**

### **Run Tests**
```bash
# Test complete system
python verify_mongodb_single_source_truth.py

# Test individual components
cd backend && python -m pytest tests/
cd whatsapp && npm test
```

### **Key Files to Customize**
```bash
backend/config/business_config.py    # Your business context
backend/agents/message_agent.py      # Message generation logic
backend/tools/monday_client.py       # CRM integration (swap for your CRM)
extension/content.js                 # Browser integration
```

## 🌟 **Why This Approach Works**

### **Traditional Sales Tools**
❌ Scattered data across multiple systems
❌ Generic, template-based messaging
❌ No memory of previous interactions
❌ Manual research and personalization

### **This MongoDB-Powered System**
✅ **Single source of truth** for all lead data
✅ **AI agents with perfect memory** of every interaction
✅ **Hyper-personalized messages** that reference specific details
✅ **Automated research and context** for every outreach

## 🤝 **Contributing**

Built with the [Agno Framework](https://github.com/agno-ai/agno) and designed to showcase MongoDB's AI capabilities.

**Want to contribute?**
- 🔧 Add new CRM integrations
- 💬 Build additional messaging channels
- 🧠 Enhance AI agent capabilities
- 📊 Improve analytics and reporting

---

**⭐ Star this repo if it helps you build better AI sales tools!**

*Showcasing MongoDB as the intelligent foundation for AI agent systems.*
